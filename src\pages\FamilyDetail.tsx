import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ArrowLeft, Edit, Plus, Eye, FileText, Users, TrendingUp, Calendar } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { formatDisplayDate } from '@/utils/dateFormat';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string | null;
  mobile_number: string;
  address: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  village: string | null;
  cif_id: string | null;
  pan_card_number: string | null;
  aadhar_number: string | null;
  created_at: string;
}

interface Investment {
  id: string;
  amount: number;
  investment_date: string;
  maturity_date: string;
  maturity_amount: number;
  status: string;
  remark: string | null;
  scheme: {
    name: string;
    scheme_code: string;
  };
}

interface Transaction {
  id: string;
  amount: number;
  transaction_date: string;
  amount_type: string;
  payment_mode: string;
  reference_number: string | null;
  investment: {
    scheme: {
      name: string;
    };
  };
}

const FamilyDetail: React.FC = () => {
  const { clientUnitHash } = useParams<{ clientUnitHash: string }>();
  const navigate = useNavigate();
  const [primaryClient, setPrimaryClient] = useState<Client | null>(null);
  const [secondaryClient, setSecondaryClient] = useState<Client | null>(null);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalInvestments: 0,
    activeInvestments: 0,
    totalAmount: 0,
    activeAmount: 0,
    maturedAmount: 0
  });

  useEffect(() => {
    if (clientUnitHash) {
      fetchFamilyDetails();
    }
  }, [clientUnitHash]);

  const fetchFamilyDetails = async () => {
    try {
      setLoading(true);

      // Fetch investments for this client unit
      const { data: investmentData, error: investmentError } = await supabase
        .from('investments')
        .select(`
          *,
          scheme:schemes(name, scheme_code),
          client:clients!client_id(
            id, first_name, last_name, email, mobile_number,
            address, city, state, pincode, village, cif_id,
            pan_card_number, aadhar_number, created_at
          ),
          secondary_applicant:clients!second_applicant_id(
            id, first_name, last_name, email, mobile_number,
            address, city, state, pincode, village, cif_id,
            pan_card_number, aadhar_number, created_at
          )
        `)
        .eq('client_unit_hash', clientUnitHash)
        .eq('is_active', true)
        .order('investment_date', { ascending: false });

      if (investmentError) throw investmentError;

      setInvestments(investmentData || []);

      // Set primary and secondary clients from the first investment
      if (investmentData && investmentData.length > 0) {
        const firstInvestment = investmentData[0];
        setPrimaryClient(firstInvestment.client);
        setSecondaryClient(firstInvestment.secondary_applicant);
      }

      // Fetch transactions for all investments in this family unit
      const investmentIds = (investmentData || []).map(inv => inv.id);
      if (investmentIds.length > 0) {
        const { data: transactionData, error: transactionError } = await supabase
          .from('transactions')
          .select(`
            *,
            investment:investments!inner(
              scheme:schemes(name)
            )
          `)
          .in('investment_id', investmentIds)
          .order('transaction_date', { ascending: false });

        if (transactionError) throw transactionError;
        setTransactions(transactionData || []);
      }

      // Calculate stats
      const totalInvestments = investmentData?.length || 0;
      const activeInvestments = investmentData?.filter(inv => inv.status === 'active').length || 0;
      const totalAmount = investmentData?.reduce((sum, inv) => sum + Number(inv.amount || 0), 0) || 0;
      const activeAmount = investmentData?.filter(inv => inv.status === 'active').reduce((sum, inv) => sum + Number(inv.amount || 0), 0) || 0;
      const maturedAmount = investmentData?.filter(inv => inv.status === 'matured').reduce((sum, inv) => sum + Number(inv.maturity_amount || 0), 0) || 0;

      setStats({
        totalInvestments,
        activeInvestments,
        totalAmount,
        activeAmount,
        maturedAmount
      });

    } catch (error) {
      console.error('Error fetching family details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch family details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return '₹' + amount.toLocaleString('en-IN', { maximumFractionDigits: 2 });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, label: 'Active' },
      matured: { variant: 'secondary' as const, label: 'Matured' },
      withdrawn: { variant: 'outline' as const, label: 'Withdrawn' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getAmountTypeColor = (type: string) => {
    switch (type) {
      case 'investment': return 'bg-green-100 text-green-800 border-green-200';
      case 'reinvestment': return 'bg-green-100 text-green-800 border-green-200';
      case 'interest_payout': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'withdrawal': return 'bg-red-100 text-red-800 border-red-200';
      case 'penalty': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'commission': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading family details...</div>;
  }

  if (!primaryClient) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Family not found</h3>
        <p className="text-gray-500 mb-4">The requested family unit could not be found.</p>
        <Button onClick={() => navigate('/family')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Families
        </Button>
      </div>
    );
  }

  const familyType = secondaryClient ? 'Joint Family' : 'Individual Family';
  const familyName = secondaryClient
    ? `${primaryClient.first_name} ${primaryClient.last_name} & ${secondaryClient.first_name} ${secondaryClient.last_name}`
    : `${primaryClient.first_name} ${primaryClient.last_name}`;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate('/family')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{familyName}</h1>
            <p className="text-gray-600">{familyType}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
          <Button onClick={() => navigate('/investments/new')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Investment
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Investments</p>
                <p className="text-2xl font-bold">{stats.totalInvestments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calendar className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active</p>
                <p className="text-2xl font-bold">{stats.activeInvestments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Total Amount</p>
              <p className="text-xl font-bold text-blue-600">{formatCurrency(stats.totalAmount)}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Active Amount</p>
              <p className="text-xl font-bold text-green-600">{formatCurrency(stats.activeAmount)}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Matured Amount</p>
              <p className="text-xl font-bold text-orange-600">{formatCurrency(stats.maturedAmount)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Client Details Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Primary Client */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Primary Client
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Full Name</label>
                <p className="font-medium">{primaryClient.first_name} {primaryClient.last_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Mobile Number</label>
                <p className="font-medium">{primaryClient.mobile_number}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p className="font-medium">{primaryClient.email || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">CIF ID</label>
                <p className="font-medium text-blue-600">{primaryClient.cif_id || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Village</label>
                <p className="font-medium">{primaryClient.village || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">PAN Card</label>
                <p className="font-medium">{primaryClient.pan_card_number || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Aadhar Number</label>
                <p className="font-medium">{primaryClient.aadhar_number || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Joined Date</label>
                <p className="font-medium">{formatDisplayDate(primaryClient.created_at)}</p>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Address</label>
              <p className="font-medium">
                {primaryClient.address || 'N/A'}
                {primaryClient.city && primaryClient.state && (
                  <span className="text-gray-500">
                    <br />{primaryClient.city}, {primaryClient.state} - {primaryClient.pincode}
                  </span>
                )}
              </p>
            </div>
            <div className="pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`/clients/${primaryClient.id}`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Secondary Client */}
        {secondaryClient ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Secondary Client
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Name</label>
                  <p className="font-medium">{secondaryClient.first_name} {secondaryClient.last_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Mobile Number</label>
                  <p className="font-medium">{secondaryClient.mobile_number}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="font-medium">{secondaryClient.email || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">CIF ID</label>
                  <p className="font-medium text-blue-600">{secondaryClient.cif_id || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Village</label>
                  <p className="font-medium">{secondaryClient.village || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">PAN Card</label>
                  <p className="font-medium">{secondaryClient.pan_card_number || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Aadhar Number</label>
                  <p className="font-medium">{secondaryClient.aadhar_number || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Joined Date</label>
                  <p className="font-medium">{formatDisplayDate(secondaryClient.created_at)}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Address</label>
                <p className="font-medium">
                  {secondaryClient.address || 'N/A'}
                  {secondaryClient.city && secondaryClient.state && (
                    <span className="text-gray-500">
                      <br />{secondaryClient.city}, {secondaryClient.state} - {secondaryClient.pincode}
                    </span>
                  )}
                </p>
              </div>
              <div className="pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/clients/${secondaryClient.id}`)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-gray-400" />
                Secondary Client
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No secondary client for this family unit</p>
                <p className="text-sm text-gray-400 mt-2">This is an individual family</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Investments Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Family Investments ({investments.length})
            </div>
            <Button onClick={() => navigate('/investments/new')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Investment
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {investments.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Scheme</TableHead>
                    <TableHead className="min-w-[120px] text-right font-semibold">Amount</TableHead>
                    <TableHead className="min-w-[120px] font-semibold">Investment Date</TableHead>
                    <TableHead className="min-w-[120px] font-semibold">Maturity Date</TableHead>
                    <TableHead className="min-w-[120px] text-right font-semibold">Maturity Amount</TableHead>
                    <TableHead className="min-w-[100px] text-center font-semibold">Status</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">Remarks</TableHead>
                    <TableHead className="min-w-[100px] text-right sticky right-0 bg-gray-50 font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {investments.map((investment, index) => (
                    <TableRow key={investment.id} className="hover:bg-gray-50">
                      <TableCell className="w-16 text-center font-medium">
                        <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                      </TableCell>
                      <TableCell className="min-w-[150px]">
                        <div>
                          <div className="font-medium text-gray-900">{investment.scheme.name}</div>
                          <div className="text-sm text-gray-500">{investment.scheme.scheme_code}</div>
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px] text-right">
                        <div className="font-medium text-green-600">
                          {formatCurrency(investment.amount)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="text-sm font-medium">
                          {formatDisplayDate(investment.investment_date)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="text-sm font-medium">
                          {formatDisplayDate(investment.maturity_date)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px] text-right">
                        <div className="font-medium text-blue-600">
                          {formatCurrency(investment.maturity_amount)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[100px] text-center">
                        {getStatusBadge(investment.status)}
                      </TableCell>
                      <TableCell className="min-w-[200px]">
                        <div className="text-sm text-gray-600 truncate max-w-[200px]">
                          {investment.remark || 'No remarks'}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[100px] text-right sticky right-0 bg-white">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/investments/${investment.id}`)}
                            className="h-8 w-8 p-0 hover:bg-blue-50"
                            title="View Investment"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/investments/${investment.id}/edit`)}
                            className="h-8 w-8 p-0 hover:bg-blue-50"
                            title="Edit Investment"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No investments found</h3>
              <p className="text-gray-500 mb-4">This family unit has no investments yet.</p>
              <Button onClick={() => navigate('/investments/new')}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Investment
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Transactions Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Transactions ({transactions.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {transactions.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Scheme</TableHead>
                    <TableHead className="min-w-[120px] text-right font-semibold">Amount</TableHead>
                    <TableHead className="min-w-[120px] font-semibold">Transaction Date</TableHead>
                    <TableHead className="min-w-[100px] font-semibold">Type</TableHead>
                    <TableHead className="min-w-[120px] font-semibold">Payment Mode</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Reference</TableHead>
                    <TableHead className="min-w-[100px] text-right sticky right-0 bg-gray-50 font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction, index) => (
                    <TableRow key={transaction.id} className="hover:bg-gray-50">
                      <TableCell className="w-16 text-center font-medium">
                        <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                      </TableCell>
                      <TableCell className="min-w-[150px]">
                        <div className="font-medium text-gray-900">
                          {transaction.investment.scheme.name}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px] text-right">
                        <div className={`font-medium ${['investment', 'reinvestment'].includes(transaction.amount_type)
                          ? 'text-green-600'
                          : 'text-red-600'
                          }`}>
                          {['investment', 'reinvestment'].includes(transaction.amount_type) ? '+' : '-'}{formatCurrency(transaction.amount)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="text-sm font-medium">
                          {formatDisplayDate(transaction.transaction_date)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[100px]">
                        <Badge variant="outline" className={`capitalize ${getAmountTypeColor(transaction.amount_type)}`}>
                          {transaction.amount_type.replace(/_/g, ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="text-sm font-medium">
                          {transaction.payment_mode || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[150px]">
                        <div className="text-sm text-gray-600 truncate max-w-[150px]">
                          {transaction.reference_number || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[100px] text-right sticky right-0 bg-white">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/transactions/${transaction.id}`)}
                            className="h-8 w-8 p-0 hover:bg-blue-50"
                            title="View Transaction"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
              <p className="text-gray-500 mb-4">No transactions have been recorded for this family unit yet.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FamilyDetail;
